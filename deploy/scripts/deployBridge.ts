import hre from "hardhat";
const { ethers, upgrades } = hre;

async function main() {
  // Get deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with:", deployer.address);

  // Deploy the bridge implementation
  const ICMBridge = await ethers.getContractFactory("ICMBridge");

  // Example: using a dummy chain ID for localnet
  const chainId = ethers.utils.formatBytes32String("LOCAL");

  // Deploy as upgradeable contract
  const bridge = await upgrades.deployProxy(ICMBridge, [
    "******************************************", // ICM messenger address (mock for local)
    ethers.utils.parseEther("0.01"),               // Bridge fee in ETH
    deployer.address                                // Fee recipient
  ], {
    initializer: "initialize",
    kind: "transparent",
  });

  await bridge.deployed();
  console.log("ICMBridge deployed at:", bridge.address);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
