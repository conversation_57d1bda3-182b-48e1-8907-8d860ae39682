{"devDependencies": {"@nomicfoundation/hardhat-ignition": "^3.0.0", "@nomicfoundation/hardhat-toolbox-viem": "^5.0.0", "@nomiclabs/hardhat-ethers": "^2.2.3", "@openzeppelin/contracts-upgradeable": "^5.4.0", "@openzeppelin/hardhat-upgrades": "^3.9.1", "@types/node": "^22.17.2", "ethers": "^6.15.0", "forge-std": "github:foundry-rs/forge-std#v1.9.4", "hardhat": "^3.0.0", "typescript": "~5.8.3", "viem": "^2.33.3"}, "type": "module", "dependencies": {"@openzeppelin/contracts": "^5.4.0"}}