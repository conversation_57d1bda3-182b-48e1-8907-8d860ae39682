hoistPattern:
  - '*'
hoistedDependencies:
  '@actions/core@1.11.1':
    '@actions/core': private
  '@actions/exec@1.1.1':
    '@actions/exec': private
  '@actions/http-client@2.2.3':
    '@actions/http-client': private
  '@actions/io@1.1.3':
    '@actions/io': private
  '@adraffy/ens-normalize@1.10.1':
    '@adraffy/ens-normalize': private
  '@aws-crypto/crc32@5.2.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/sha256-browser@5.2.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@5.2.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@5.2.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@5.2.0':
    '@aws-crypto/util': private
  '@aws-sdk/client-lambda@3.865.0':
    '@aws-sdk/client-lambda': private
  '@aws-sdk/client-sso@3.864.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/core@3.864.0':
    '@aws-sdk/core': private
  '@aws-sdk/credential-provider-env@3.864.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-http@3.864.0':
    '@aws-sdk/credential-provider-http': private
  '@aws-sdk/credential-provider-ini@3.864.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.864.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.864.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.864.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.864.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/middleware-host-header@3.862.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-logger@3.862.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.862.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-user-agent@3.864.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/nested-clients@3.864.0':
    '@aws-sdk/nested-clients': private
  '@aws-sdk/region-config-resolver@3.862.0':
    '@aws-sdk/region-config-resolver': private
  '@aws-sdk/token-providers@3.864.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.862.0':
    '@aws-sdk/types': private
  '@aws-sdk/util-endpoints@3.862.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-locate-window@3.804.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-user-agent-browser@3.862.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.864.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/util-utf8-browser@3.259.0':
    '@aws-sdk/util-utf8-browser': private
  '@aws-sdk/xml-builder@3.862.0':
    '@aws-sdk/xml-builder': private
  '@bytecodealliance/preview2-shim@0.17.0':
    '@bytecodealliance/preview2-shim': private
  '@esbuild/aix-ppc64@0.25.9':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.9':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.9':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.9':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.9':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.9':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.9':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.9':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.9':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.9':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.9':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.9':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.9':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.9':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.9':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.9':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.9':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.9':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.9':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.9':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.9':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.9':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.9':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.9':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.9':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': private
  '@ethersproject/abi@5.8.0':
    '@ethersproject/abi': private
  '@ethersproject/abstract-provider@5.8.0':
    '@ethersproject/abstract-provider': private
  '@ethersproject/abstract-signer@5.8.0':
    '@ethersproject/abstract-signer': private
  '@ethersproject/address@5.6.1':
    '@ethersproject/address': private
  '@ethersproject/base64@5.8.0':
    '@ethersproject/base64': private
  '@ethersproject/bignumber@5.8.0':
    '@ethersproject/bignumber': private
  '@ethersproject/bytes@5.8.0':
    '@ethersproject/bytes': private
  '@ethersproject/constants@5.8.0':
    '@ethersproject/constants': private
  '@ethersproject/hash@5.8.0':
    '@ethersproject/hash': private
  '@ethersproject/keccak256@5.8.0':
    '@ethersproject/keccak256': private
  '@ethersproject/logger@5.8.0':
    '@ethersproject/logger': private
  '@ethersproject/networks@5.8.0':
    '@ethersproject/networks': private
  '@ethersproject/properties@5.8.0':
    '@ethersproject/properties': private
  '@ethersproject/rlp@5.8.0':
    '@ethersproject/rlp': private
  '@ethersproject/signing-key@5.8.0':
    '@ethersproject/signing-key': private
  '@ethersproject/strings@5.8.0':
    '@ethersproject/strings': private
  '@ethersproject/transactions@5.8.0':
    '@ethersproject/transactions': private
  '@ethersproject/web@5.8.0':
    '@ethersproject/web': private
  '@fastify/busboy@2.1.1':
    '@fastify/busboy': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@noble/ciphers@1.2.1':
    '@noble/ciphers': private
  '@noble/curves@1.2.0':
    '@noble/curves': private
  '@noble/hashes@1.3.2':
    '@noble/hashes': private
  '@nomicfoundation/edr-darwin-arm64@0.12.0-next.4':
    '@nomicfoundation/edr-darwin-arm64': private
  '@nomicfoundation/edr-darwin-x64@0.12.0-next.4':
    '@nomicfoundation/edr-darwin-x64': private
  '@nomicfoundation/edr-linux-arm64-gnu@0.12.0-next.4':
    '@nomicfoundation/edr-linux-arm64-gnu': private
  '@nomicfoundation/edr-linux-arm64-musl@0.12.0-next.4':
    '@nomicfoundation/edr-linux-arm64-musl': private
  '@nomicfoundation/edr-linux-x64-gnu@0.12.0-next.4':
    '@nomicfoundation/edr-linux-x64-gnu': private
  '@nomicfoundation/edr-linux-x64-musl@0.12.0-next.4':
    '@nomicfoundation/edr-linux-x64-musl': private
  '@nomicfoundation/edr-win32-x64-msvc@0.12.0-next.4':
    '@nomicfoundation/edr-win32-x64-msvc': private
  '@nomicfoundation/edr@0.12.0-next.4':
    '@nomicfoundation/edr': private
  '@nomicfoundation/hardhat-errors@3.0.0':
    '@nomicfoundation/hardhat-errors': private
  '@nomicfoundation/hardhat-ethers@3.1.0(ethers@6.15.0)(hardhat@3.0.0)':
    '@nomicfoundation/hardhat-ethers': private
  '@nomicfoundation/hardhat-ignition-viem@3.0.0(@nomicfoundation/hardhat-ignition@3.0.0(@nomicfoundation/hardhat-verify@3.0.0(hardhat@3.0.0))(hardhat@3.0.0))(@nomicfoundation/hardhat-verify@3.0.0(hardhat@3.0.0))(@nomicfoundation/hardhat-viem@3.0.0(hardhat@3.0.0)(viem@2.33.3(typescript@5.8.3)(zod@3.25.76)))(@nomicfoundation/ignition-core@3.0.0)(hardhat@3.0.0)(viem@2.33.3(typescript@5.8.3)(zod@3.25.76))':
    '@nomicfoundation/hardhat-ignition-viem': private
  '@nomicfoundation/hardhat-keystore@3.0.0(hardhat@3.0.0)':
    '@nomicfoundation/hardhat-keystore': private
  '@nomicfoundation/hardhat-network-helpers@3.0.0(hardhat@3.0.0)':
    '@nomicfoundation/hardhat-network-helpers': private
  '@nomicfoundation/hardhat-node-test-reporter@3.0.0':
    '@nomicfoundation/hardhat-node-test-reporter': private
  '@nomicfoundation/hardhat-node-test-runner@3.0.0(hardhat@3.0.0)':
    '@nomicfoundation/hardhat-node-test-runner': private
  '@nomicfoundation/hardhat-utils@3.0.0':
    '@nomicfoundation/hardhat-utils': private
  '@nomicfoundation/hardhat-verify@3.0.0(hardhat@3.0.0)':
    '@nomicfoundation/hardhat-verify': private
  '@nomicfoundation/hardhat-viem-assertions@3.0.0(@nomicfoundation/hardhat-viem@3.0.0(hardhat@3.0.0)(viem@2.33.3(typescript@5.8.3)(zod@3.25.76)))(hardhat@3.0.0)(viem@2.33.3(typescript@5.8.3)(zod@3.25.76))':
    '@nomicfoundation/hardhat-viem-assertions': private
  '@nomicfoundation/hardhat-viem@3.0.0(hardhat@3.0.0)(viem@2.33.3(typescript@5.8.3)(zod@3.25.76))':
    '@nomicfoundation/hardhat-viem': private
  '@nomicfoundation/hardhat-zod-utils@3.0.0(zod@3.25.76)':
    '@nomicfoundation/hardhat-zod-utils': private
  '@nomicfoundation/ignition-core@3.0.0':
    '@nomicfoundation/ignition-core': private
  '@nomicfoundation/ignition-ui@3.0.0':
    '@nomicfoundation/ignition-ui': private
  '@nomicfoundation/slang@0.18.3':
    '@nomicfoundation/slang': private
  '@nomicfoundation/solidity-analyzer-darwin-arm64@0.1.2':
    '@nomicfoundation/solidity-analyzer-darwin-arm64': private
  '@nomicfoundation/solidity-analyzer-darwin-x64@0.1.2':
    '@nomicfoundation/solidity-analyzer-darwin-x64': private
  '@nomicfoundation/solidity-analyzer-linux-arm64-gnu@0.1.2':
    '@nomicfoundation/solidity-analyzer-linux-arm64-gnu': private
  '@nomicfoundation/solidity-analyzer-linux-arm64-musl@0.1.2':
    '@nomicfoundation/solidity-analyzer-linux-arm64-musl': private
  '@nomicfoundation/solidity-analyzer-linux-x64-gnu@0.1.2':
    '@nomicfoundation/solidity-analyzer-linux-x64-gnu': private
  '@nomicfoundation/solidity-analyzer-linux-x64-musl@0.1.2':
    '@nomicfoundation/solidity-analyzer-linux-x64-musl': private
  '@nomicfoundation/solidity-analyzer-win32-x64-msvc@0.1.2':
    '@nomicfoundation/solidity-analyzer-win32-x64-msvc': private
  '@nomicfoundation/solidity-analyzer@0.1.2':
    '@nomicfoundation/solidity-analyzer': private
  '@openzeppelin/contracts@5.4.0':
    '@openzeppelin/contracts': private
  '@openzeppelin/defender-sdk-base-client@2.7.0':
    '@openzeppelin/defender-sdk-base-client': private
  '@openzeppelin/defender-sdk-deploy-client@2.7.0(debug@4.4.1)':
    '@openzeppelin/defender-sdk-deploy-client': private
  '@openzeppelin/defender-sdk-network-client@2.7.0(debug@4.4.1)':
    '@openzeppelin/defender-sdk-network-client': private
  '@openzeppelin/upgrades-core@1.44.1':
    '@openzeppelin/upgrades-core': private
  '@scure/base@1.2.6':
    '@scure/base': private
  '@scure/bip32@1.7.0':
    '@scure/bip32': private
  '@scure/bip39@1.6.0':
    '@scure/bip39': private
  '@sentry/core@9.46.0':
    '@sentry/core': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@smithy/abort-controller@4.0.5':
    '@smithy/abort-controller': private
  '@smithy/config-resolver@4.1.5':
    '@smithy/config-resolver': private
  '@smithy/core@3.8.0':
    '@smithy/core': private
  '@smithy/credential-provider-imds@4.0.7':
    '@smithy/credential-provider-imds': private
  '@smithy/eventstream-codec@4.0.5':
    '@smithy/eventstream-codec': private
  '@smithy/eventstream-serde-browser@4.0.5':
    '@smithy/eventstream-serde-browser': private
  '@smithy/eventstream-serde-config-resolver@4.1.3':
    '@smithy/eventstream-serde-config-resolver': private
  '@smithy/eventstream-serde-node@4.0.5':
    '@smithy/eventstream-serde-node': private
  '@smithy/eventstream-serde-universal@4.0.5':
    '@smithy/eventstream-serde-universal': private
  '@smithy/fetch-http-handler@5.1.1':
    '@smithy/fetch-http-handler': private
  '@smithy/hash-node@4.0.5':
    '@smithy/hash-node': private
  '@smithy/invalid-dependency@4.0.5':
    '@smithy/invalid-dependency': private
  '@smithy/is-array-buffer@4.0.0':
    '@smithy/is-array-buffer': private
  '@smithy/middleware-content-length@4.0.5':
    '@smithy/middleware-content-length': private
  '@smithy/middleware-endpoint@4.1.18':
    '@smithy/middleware-endpoint': private
  '@smithy/middleware-retry@4.1.19':
    '@smithy/middleware-retry': private
  '@smithy/middleware-serde@4.0.9':
    '@smithy/middleware-serde': private
  '@smithy/middleware-stack@4.0.5':
    '@smithy/middleware-stack': private
  '@smithy/node-config-provider@4.1.4':
    '@smithy/node-config-provider': private
  '@smithy/node-http-handler@4.1.1':
    '@smithy/node-http-handler': private
  '@smithy/property-provider@4.0.5':
    '@smithy/property-provider': private
  '@smithy/protocol-http@5.1.3':
    '@smithy/protocol-http': private
  '@smithy/querystring-builder@4.0.5':
    '@smithy/querystring-builder': private
  '@smithy/querystring-parser@4.0.5':
    '@smithy/querystring-parser': private
  '@smithy/service-error-classification@4.0.7':
    '@smithy/service-error-classification': private
  '@smithy/shared-ini-file-loader@4.0.5':
    '@smithy/shared-ini-file-loader': private
  '@smithy/signature-v4@5.1.3':
    '@smithy/signature-v4': private
  '@smithy/smithy-client@4.4.10':
    '@smithy/smithy-client': private
  '@smithy/types@4.3.2':
    '@smithy/types': private
  '@smithy/url-parser@4.0.5':
    '@smithy/url-parser': private
  '@smithy/util-base64@4.0.0':
    '@smithy/util-base64': private
  '@smithy/util-body-length-browser@4.0.0':
    '@smithy/util-body-length-browser': private
  '@smithy/util-body-length-node@4.0.0':
    '@smithy/util-body-length-node': private
  '@smithy/util-buffer-from@4.0.0':
    '@smithy/util-buffer-from': private
  '@smithy/util-config-provider@4.0.0':
    '@smithy/util-config-provider': private
  '@smithy/util-defaults-mode-browser@4.0.26':
    '@smithy/util-defaults-mode-browser': private
  '@smithy/util-defaults-mode-node@4.0.26':
    '@smithy/util-defaults-mode-node': private
  '@smithy/util-endpoints@3.0.7':
    '@smithy/util-endpoints': private
  '@smithy/util-hex-encoding@4.0.0':
    '@smithy/util-hex-encoding': private
  '@smithy/util-middleware@4.0.5':
    '@smithy/util-middleware': private
  '@smithy/util-retry@4.0.7':
    '@smithy/util-retry': private
  '@smithy/util-stream@4.2.4':
    '@smithy/util-stream': private
  '@smithy/util-uri-escape@4.0.0':
    '@smithy/util-uri-escape': private
  '@smithy/util-utf8@4.0.0':
    '@smithy/util-utf8': private
  '@smithy/util-waiter@4.0.7':
    '@smithy/util-waiter': private
  '@streamparser/json-node@0.0.22':
    '@streamparser/json-node': private
  '@streamparser/json@0.0.22':
    '@streamparser/json': private
  '@types/bn.js@5.2.0':
    '@types/bn.js': private
  '@types/pbkdf2@3.1.2':
    '@types/pbkdf2': private
  '@types/secp256k1@4.0.6':
    '@types/secp256k1': private
  '@types/uuid@9.0.8':
    '@types/uuid': private
  abitype@1.0.8(typescript@5.8.3)(zod@3.25.76):
    abitype: private
  adm-zip@0.4.16:
    adm-zip: private
  aes-js@4.0.0-beta.5:
    aes-js: private
  amazon-cognito-identity-js@6.3.15:
    amazon-cognito-identity-js: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  async-retry@1.3.3:
    async-retry: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axios@1.11.0(debug@4.4.1):
    axios: private
  balanced-match@1.0.2:
    balanced-match: private
  base-x@3.0.11:
    base-x: private
  base64-js@1.5.1:
    base64-js: private
  bignumber.js@9.3.1:
    bignumber.js: private
  blakejs@1.2.1:
    blakejs: private
  bn.js@5.2.2:
    bn.js: private
  bowser@2.12.0:
    bowser: private
  brace-expansion@2.0.2:
    brace-expansion: private
  brorand@1.1.0:
    brorand: private
  browserify-aes@1.2.0:
    browserify-aes: private
  bs58@4.0.1:
    bs58: private
  bs58check@2.1.2:
    bs58check: private
  buffer-xor@1.0.3:
    buffer-xor: private
  buffer@4.9.2:
    buffer: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  cbor2@1.12.0:
    cbor2: private
  cbor@10.0.10:
    cbor: private
  chalk@5.5.0:
    chalk: private
  cipher-base@1.0.6:
    cipher-base: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  compare-versions@6.1.1:
    compare-versions: private
  create-hash@1.2.0:
    create-hash: private
  create-hmac@1.1.7:
    create-hmac: private
  debug@4.4.1:
    debug: private
  define-data-property@1.1.4:
    define-data-property: private
  delayed-stream@1.0.0:
    delayed-stream: private
  diff-sequences@29.6.3:
    diff-sequences: private
  dunder-proto@1.0.1:
    dunder-proto: private
  elliptic@6.6.1:
    elliptic: private
  enquirer@2.4.1:
    enquirer: private
  env-paths@2.2.1:
    env-paths: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.9:
    esbuild: private
  ethereum-cryptography@2.2.1:
    ethereum-cryptography: private
  ethereumjs-util@7.1.5:
    ethereumjs-util: private
  eventemitter3@5.0.1:
    eventemitter3: private
  evp_bytestokey@1.0.3:
    evp_bytestokey: private
  fast-base64-decode@1.0.0:
    fast-base64-decode: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-xml-parser@5.2.5:
    fast-xml-parser: private
  follow-redirects@1.15.11(debug@4.4.1):
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  form-data@4.0.4:
    form-data: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hash-base@3.1.0:
    hash-base: private
  hash.js@1.1.7:
    hash.js: private
  hasown@2.0.2:
    hasown: private
  hmac-drbg@1.0.1:
    hmac-drbg: private
  ieee754@1.2.1:
    ieee754: private
  immer@10.0.2:
    immer: private
  inherits@2.0.4:
    inherits: private
  is-callable@1.2.7:
    is-callable: private
  is-typed-array@1.1.15:
    is-typed-array: private
  isarray@1.0.0:
    isarray: private
  isomorphic-unfetch@3.1.0:
    isomorphic-unfetch: private
  isows@1.0.7(ws@8.18.2):
    isows: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-get-type@29.6.3:
    jest-get-type: private
  js-cookie@2.2.1:
    js-cookie: private
  js-sha3@0.8.0:
    js-sha3: private
  json-stream-stringify@3.1.6:
    json-stream-stringify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  keccak@3.0.4:
    keccak: private
  kleur@3.0.3:
    kleur: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash@4.17.21:
    lodash: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  md5.js@1.3.5:
    md5.js: private
  micro-eth-signer@0.14.0:
    micro-eth-signer: private
  micro-packed@0.7.3:
    micro-packed: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimalistic-crypto-utils@1.0.1:
    minimalistic-crypto-utils: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  ms@2.1.3:
    ms: private
  ndjson@2.0.0:
    ndjson: private
  node-addon-api@2.0.2:
    node-addon-api: private
  node-fetch@2.7.0:
    node-fetch: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  nofilter@3.1.0:
    nofilter: private
  ox@0.8.6(typescript@5.8.3)(zod@3.25.76):
    ox: private
  p-map@7.0.3:
    p-map: private
  pbkdf2@3.1.3:
    pbkdf2: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  pretty-format@29.7.0:
    pretty-format: private
  prompts@2.4.2:
    prompts: private
  proper-lockfile@4.1.2:
    proper-lockfile: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  randombytes@2.1.0:
    randombytes: private
  react-is@18.3.1:
    react-is: private
  readable-stream@3.6.2:
    readable-stream: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.3:
    resolve.exports: private
  retry@0.12.0:
    retry: private
  rfdc@1.4.1:
    rfdc: private
  ripemd160@2.0.2:
    ripemd160: private
  rlp@2.2.7:
    rlp: private
  safe-buffer@5.2.1:
    safe-buffer: private
  scrypt-js@3.0.1:
    scrypt-js: private
  secp256k1@4.0.4:
    secp256k1: private
  semver@7.7.2:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  setimmediate@1.0.5:
    setimmediate: private
  sha.js@2.4.12:
    sha.js: private
  signal-exit@3.0.7:
    signal-exit: private
  sisteransi@1.0.5:
    sisteransi: private
  solidity-ast@0.4.60:
    solidity-ast: private
  split2@3.2.2:
    split2: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strnum@2.1.1:
    strnum: private
  supports-color@7.2.0:
    supports-color: private
  through2@4.0.2:
    through2: private
  to-buffer@1.2.1:
    to-buffer: private
  tr46@0.0.3:
    tr46: private
  tslib@2.7.0:
    tslib: private
  tsx@4.20.4:
    tsx: private
  tunnel@0.0.6:
    tunnel: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  undici-types@6.21.0:
    undici-types: private
  undici@6.21.3:
    undici: private
  unfetch@4.2.0:
    unfetch: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@9.0.1:
    uuid: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-typed-array@1.1.19:
    which-typed-array: private
  ws@8.17.1:
    ws: private
  zod@3.25.76:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.10.0
pendingBuilds: []
prunedAt: Sun, 17 Aug 2025 05:37:26 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-arm64@0.25.9'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.25.9'
  - '@esbuild/win32-x64@0.25.9'
  - fsevents@2.3.3
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
