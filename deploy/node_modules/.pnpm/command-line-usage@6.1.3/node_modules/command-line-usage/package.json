{"name": "command-line-usage", "author": "<PERSON> <<EMAIL>>", "version": "6.1.3", "description": "Generates command-line usage information", "repository": "https://github.com/75lb/command-line-usage", "license": "MIT", "files": ["lib/**/*.js", "index.js"], "keywords": ["terminal", "command line", "usage", "generator"], "engines": {"node": ">=8.0.0"}, "scripts": {"docs": "jsdoc2md --no-gfm index.js lib/**/*.js > doc/api.md; echo", "test": "test-runner test/*.js", "cover": "nyc npm test && nyc report --reporter=text-lcov | coveralls"}, "dependencies": {"array-back": "^4.0.2", "chalk": "^2.4.2", "table-layout": "^1.0.2", "typical": "^5.2.0"}, "devDependencies": {"jsdoc-to-markdown": "^7.1.1", "test-runner": "^0.6.3"}, "standard": {"ignore": ["example"]}}