{"version": 3, "file": "estimateMaxPriorityFeePerGas.js", "sourceRoot": "", "sources": ["../../../actions/public/estimateMaxPriorityFeePerGas.ts"], "names": [], "mappings": ";;AA2DA,oEAUC;AAED,sFA2DC;AAhID,gDAG4B;AAO5B,gEAGwC;AACxC,2DAAoD;AAEpD,+CAAgE;AAChE,qDAAyE;AAwClE,KAAK,UAAU,4BAA4B,CAIhD,MAAgC,EAChC,IAEa;IAEb,OAAO,qCAAqC,CAAC,MAAM,EAAE,IAAW,CAAC,CAAA;AACnE,CAAC;AAEM,KAAK,UAAU,qCAAqC,CAIzD,MAAgC,EAChC,IASC;IAED,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,IAAI,EAAE,CAAA;IAEnE,IAAI,CAAC;QACH,MAAM,oBAAoB,GACxB,KAAK,EAAE,IAAI,EAAE,oBAAoB,IAAI,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAA;QAEtE,IAAI,OAAO,oBAAoB,KAAK,UAAU,EAAE,CAAC;YAC/C,MAAM,KAAK,GACT,MAAM,IAAI,CAAC,MAAM,IAAA,wBAAS,EAAC,MAAM,EAAE,sBAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAC/D,MAAM,qBAAqB,GAAG,MAAM,oBAAoB,CAAC;gBACvD,KAAK;gBACL,MAAM;gBACN,OAAO;aACiB,CAAC,CAAA;YAC3B,IAAI,qBAAqB,KAAK,IAAI;gBAAE,MAAM,IAAI,KAAK,EAAE,CAAA;YACrD,OAAO,qBAAqB,CAAA;QAC9B,CAAC;QAED,IAAI,OAAO,oBAAoB,KAAK,WAAW;YAAE,OAAO,oBAAoB,CAAA;QAE5E,MAAM,uBAAuB,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC;YACnD,MAAM,EAAE,0BAA0B;SACnC,CAAC,CAAA;QACF,OAAO,IAAA,wBAAW,EAAC,uBAAuB,CAAC,CAAA;IAC7C,CAAC;IAAC,MAAM,CAAC;QAIP,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC1C,MAAM;gBACJ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;gBACzB,CAAC,CAAC,IAAA,wBAAS,EAAC,MAAM,EAAE,sBAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC;YAC/C,IAAA,wBAAS,EAAC,MAAM,EAAE,4BAAW,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC;SAClD,CAAC,CAAA;QAEF,IAAI,OAAO,KAAK,CAAC,aAAa,KAAK,QAAQ;YACzC,MAAM,IAAI,qCAA4B,EAAE,CAAA;QAE1C,MAAM,oBAAoB,GAAG,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAA;QAE3D,IAAI,oBAAoB,GAAG,EAAE;YAAE,OAAO,EAAE,CAAA;QACxC,OAAO,oBAAoB,CAAA;IAC7B,CAAC;AACH,CAAC"}