{"version": 3, "file": "simulateBlocks.js", "sourceRoot": "", "sources": ["../../../actions/public/simulateBlocks.ts"], "names": [], "mappings": ";;AAmLA,wCAsHC;AAxSD,oDAAmD;AAEnD,0EAG6C;AAG7C,gDAA8D;AAE9D,0DAA2D;AAC3D,kDAAuD;AAYvD,qFAGgD;AAChD,iFAG8C;AAC9C,0DAAmD;AACnD,4DAGsC;AACtC,gFAAyE;AACzE,wEAG2C;AAC3C,8DAGwC;AACxC,0DAAyD;AACzD,wFAGqD;AACrD,mEAGqC;AACrC,+EAGiD;AAyH1C,KAAK,UAAU,cAAc,CAIlC,MAAgC,EAChC,UAA2C;IAE3C,MAAM,EACJ,WAAW,EACX,QAAQ,GAAG,MAAM,CAAC,qBAAqB,IAAI,QAAQ,EACnD,MAAM,EACN,sBAAsB,EACtB,cAAc,EACd,UAAU,GACX,GAAG,UAAU,CAAA;IAEd,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,EAAE,CAAA;QAC1B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc;gBACzC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC;gBAC5C,CAAC,CAAC,SAAS,CAAA;YACb,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACtC,MAAM,IAAI,GAAG,KAA2C,CAAA;gBACxD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAA,8BAAY,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;gBACrE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAA,0CAAkB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAA;gBAC5D,MAAM,OAAO,GAAG;oBACd,GAAG,IAAI;oBACP,IAAI,EAAE,IAAI,CAAC,UAAU;wBACnB,CAAC,CAAC,IAAA,kBAAM,EAAC,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;wBACzC,CAAC,CAAC,IAAI;oBACR,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,OAAO,EAAE,OAAO;iBAC3B,CAAA;gBACV,IAAA,gCAAa,EAAC,OAAO,CAAC,CAAA;gBACtB,OAAO,IAAA,gDAAwB,EAAC,OAAO,CAAC,CAAA;YAC1C,CAAC,CAAC,CAAA;YACF,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc;gBACzC,CAAC,CAAC,IAAA,yCAAsB,EAAC,KAAK,CAAC,cAAc,CAAC;gBAC9C,CAAC,CAAC,SAAS,CAAA;YAEb,eAAe,CAAC,IAAI,CAAC;gBACnB,cAAc;gBACd,KAAK;gBACL,cAAc;aACf,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,cAAc,GAClB,OAAO,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAA,sBAAW,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACxE,MAAM,KAAK,GAAG,cAAc,IAAI,QAAQ,CAAA;QAExC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC;YAClC,MAAM,EAAE,gBAAgB;YACxB,MAAM,EAAE;gBACN,EAAE,eAAe,EAAE,sBAAsB,EAAE,cAAc,EAAE,UAAU,EAAE;gBACvE,KAAK;aACN;SACF,CAAC,CAAA;QAEF,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAC/B,GAAG,IAAA,sBAAW,EAAC,KAAK,CAAC;YACrB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;gBACjC,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAGxD,CAAA;gBAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,IAAI,IAAI,CAAC,UAAU,CAAA;gBAChD,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAA,kBAAS,EAAC,GAAG,CAAC,CAAC,CAAA;gBACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAA;gBAE5D,MAAM,MAAM,GACV,GAAG,IAAI,MAAM,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI;oBAC1C,CAAC,CAAC,IAAA,8CAAoB,EAAC;wBACnB,GAAG;wBACH,IAAI;wBACJ,YAAY;qBACb,CAAC;oBACJ,CAAC,CAAC,IAAI,CAAA;gBAEV,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE;oBAClB,IAAI,MAAM,KAAK,SAAS;wBAAE,OAAO,SAAS,CAAA;oBAE1C,IAAI,KAAK,GAAG,SAAS,CAAA;oBACrB,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI;wBAAE,KAAK,GAAG,IAAI,iCAAwB,EAAE,CAAA;yBAChE,IAAI,IAAI,CAAC,KAAK;wBAAE,KAAK,GAAG,IAAI,8BAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBAE7D,IAAI,CAAC,KAAK;wBAAE,OAAO,SAAS,CAAA;oBAC5B,OAAO,IAAA,sCAAgB,EAAC,KAAK,EAAE;wBAC7B,GAAG,EAAE,CAAC,GAAG,IAAI,EAAE,CAAQ;wBACvB,OAAO,EAAE,EAAE,IAAI,IAAI;wBACnB,IAAI;wBACJ,YAAY,EAAE,YAAY,IAAI,WAAW;qBAC1C,CAAC,CAAA;gBACJ,CAAC,CAAC,EAAE,CAAA;gBAEJ,OAAO;oBACL,IAAI;oBACJ,OAAO;oBACP,IAAI;oBACJ,MAAM;oBACN,GAAG,CAAC,MAAM,KAAK,SAAS;wBACtB,CAAC,CAAC;4BACE,MAAM;yBACP;wBACH,CAAC,CAAC;4BACE,KAAK;yBACN,CAAC;iBACP,CAAA;YACH,CAAC,CAAC;SACH,CAAC,CAA+C,CAAA;IACnD,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,KAAK,GAAG,CAAc,CAAA;QAC5B,MAAM,KAAK,GAAG,IAAA,8BAAY,EAAC,KAAK,EAAE,EAAE,CAAC,CAAA;QACrC,IAAI,KAAK,YAAY,0BAAgB;YAAE,MAAM,KAAK,CAAA;QAClD,MAAM,KAAK,CAAA;IACb,CAAC;AACH,CAAC"}