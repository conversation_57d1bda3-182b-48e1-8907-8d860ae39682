{"version": 3, "file": "watchBlocks.js", "sourceRoot": "", "sources": ["../../../actions/public/watchBlocks.ts"], "names": [], "mappings": ";;AAiGA,kCAiLC;AA5QD,2DAAoD;AACpD,uDAAgD;AAChD,iDAA8D;AAC9D,2DAA6E;AAE7E,+CAAiE;AAsFjE,SAAgB,WAAW,CAMzB,MAAgC,EAChC,EACE,QAAQ,GAAG,MAAM,CAAC,qBAAqB,IAAI,QAAQ,EACnD,UAAU,GAAG,KAAK,EAClB,WAAW,GAAG,KAAK,EACnB,OAAO,EACP,OAAO,EACP,mBAAmB,EAAE,oBAAoB,EACzC,IAAI,EAAE,KAAK,EACX,eAAe,GAAG,MAAM,CAAC,eAAe,GAC+B;IAEzE,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE;QAC1B,IAAI,OAAO,KAAK,KAAK,WAAW;YAAE,OAAO,KAAK,CAAA;QAC9C,IACE,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW;YACrC,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,KAAK;YAE/B,OAAO,KAAK,CAAA;QACd,IACE,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU;YACpC,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW;gBACzD,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC;YAEvD,OAAO,KAAK,CAAA;QACd,OAAO,IAAI,CAAA;IACb,CAAC,CAAC,EAAE,CAAA;IACJ,MAAM,mBAAmB,GAAG,oBAAoB,IAAI,KAAK,CAAA;IAEzD,IAAI,SAES,CAAA;IAEb,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,MAAM,UAAU,GAAG,IAAA,wBAAS,EAAC;YAC3B,aAAa;YACb,MAAM,CAAC,GAAG;YACV,QAAQ;YACR,UAAU;YACV,WAAW;YACX,mBAAmB;YACnB,eAAe;SAChB,CAAC,CAAA;QAEF,OAAO,IAAA,oBAAO,EAAC,UAAU,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CACxD,IAAA,cAAI,EACF,KAAK,IAAI,EAAE;YACT,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,IAAA,wBAAS,EAC3B,MAAM,EACN,sBAAQ,EACR,UAAU,CACX,CAAC;oBACA,QAAQ;oBACR,mBAAmB;iBACpB,CAAC,CAAA;gBACF,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,IAAI,SAAS,EAAE,MAAM,IAAI,IAAI,EAAE,CAAC;oBAGvD,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;wBAAE,OAAM;oBAI7C,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;wBACtD,KAAK,IAAI,CAAC,GAAG,SAAS,EAAE,MAAM,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;4BAC3D,MAAM,KAAK,GAAG,CAAC,MAAM,IAAA,wBAAS,EAC5B,MAAM,EACN,sBAAQ,EACR,UAAU,CACX,CAAC;gCACA,WAAW,EAAE,CAAC;gCACd,mBAAmB;6BACpB,CAAC,CAA8B,CAAA;4BAChC,IAAI,CAAC,OAAO,CAAC,KAAY,EAAE,SAAgB,CAAC,CAAA;4BAC5C,SAAS,GAAG,KAAK,CAAA;wBACnB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAEE,SAAS,EAAE,MAAM,IAAI,IAAI;oBAEzB,CAAC,QAAQ,KAAK,SAAS,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,CAAC;oBAGjD,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,EAC1D,CAAC;oBACD,IAAI,CAAC,OAAO,CAAC,KAAY,EAAE,SAAgB,CAAC,CAAA;oBAC5C,SAAS,GAAG,KAAY,CAAA;gBAC1B,CAAC;YACH,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,IAAI,CAAC,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;YAC9B,CAAC;QACH,CAAC,EACD;YACE,WAAW;YACX,QAAQ,EAAE,eAAe;SAC1B,CACF,CACF,CAAA;IACH,CAAC,CAAA;IAED,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,MAAM,GAAG,IAAI,CAAA;QACjB,IAAI,WAAW,GAAG,IAAI,CAAA;QACtB,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CACvC;QAAA,CAAC,KAAK,IAAI,EAAE;YACX,IAAI,CAAC;gBACH,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAA,wBAAS,EACP,MAAM,EACN,sBAAQ,EACR,UAAU,CACX,CAAC;wBACA,QAAQ;wBACR,mBAAmB;qBACpB,CAAC;yBACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACd,IAAI,CAAC,MAAM;4BAAE,OAAM;wBACnB,IAAI,CAAC,WAAW;4BAAE,OAAM;wBACxB,OAAO,CAAC,KAAY,EAAE,SAAS,CAAC,CAAA;wBAChC,WAAW,GAAG,KAAK,CAAA;oBACrB,CAAC,CAAC;yBACD,KAAK,CAAC,OAAO,CAAC,CAAA;gBACnB,CAAC;gBAED,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE;oBACtB,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;wBACzC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAChD,CAAC,SAAgC,EAAE,EAAE,CACnC,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW;4BACrC,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAClC,CAAA;wBACD,IAAI,CAAC,SAAS;4BAAE,OAAO,MAAM,CAAC,SAAS,CAAA;wBACvC,OAAO,SAAS,CAAC,KAAK,CAAA;oBACxB,CAAC;oBACD,OAAO,MAAM,CAAC,SAAS,CAAA;gBACzB,CAAC,CAAC,EAAE,CAAA;gBAEJ,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,MAAM,SAAS,CAAC,SAAS,CAAC;oBAC9D,MAAM,EAAE,CAAC,UAAU,CAAC;oBACpB,KAAK,CAAC,MAAM,CAAC,IAAS;wBACpB,IAAI,CAAC,MAAM;4BAAE,OAAM;wBACnB,MAAM,KAAK,GAAG,CAAC,MAAM,IAAA,wBAAS,EAC5B,MAAM,EACN,sBAAQ,EACR,UAAU,CACX,CAAC;4BACA,WAAW,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM;4BAChC,mBAAmB;yBACpB,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAA8B,CAAA;wBAChD,IAAI,CAAC,MAAM;4BAAE,OAAM;wBACnB,OAAO,CAAC,KAAY,EAAE,SAAgB,CAAC,CAAA;wBACvC,WAAW,GAAG,KAAK,CAAA;wBACnB,SAAS,GAAG,KAAK,CAAA;oBACnB,CAAC;oBACD,OAAO,CAAC,KAAY;wBAClB,OAAO,EAAE,CAAC,KAAK,CAAC,CAAA;oBAClB,CAAC;iBACF,CAAC,CAAA;gBACF,WAAW,GAAG,YAAY,CAAA;gBAC1B,IAAI,CAAC,MAAM;oBAAE,WAAW,EAAE,CAAA;YAC5B,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;YACzB,CAAC;QACH,CAAC,CAAC,EAAE,CAAA;QACJ,OAAO,GAAG,EAAE,CAAC,WAAW,EAAE,CAAA;IAC5B,CAAC,CAAA;IAED,OAAO,aAAa,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAA;AACzD,CAAC"}