{"version": 3, "file": "toAccount.js", "sourceRoot": "", "sources": ["../../accounts/toAccount.ts"], "names": [], "mappings": ";;AAmCA,8BAyBC;AAxDD,qDAG6B;AAC7B,gEAGsC;AAwBtC,SAAgB,SAAS,CACvB,MAAqB;IAErB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,IAAI,CAAC,IAAA,wBAAS,EAAC,MAAM,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YACvC,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAA;QACpD,OAAO;YACL,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,UAAU;SACsB,CAAA;IAC1C,CAAC;IAED,IAAI,CAAC,IAAA,wBAAS,EAAC,MAAM,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;QAC/C,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,CAAA;IAC5D,OAAO;QACL,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,YAAY,EAAE,MAAM,CAAC,YAAY;QACjC,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,iBAAiB,EAAE,MAAM,CAAC,iBAAiB;QAC3C,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,eAAe,EAAE,MAAM,CAAC,eAAe;QACvC,aAAa,EAAE,MAAM,CAAC,aAAa;QACnC,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,OAAO;KACyB,CAAA;AAC1C,CAAC"}