{"version": 3, "file": "sign.js", "sourceRoot": "", "sources": ["../../../accounts/utils/sign.ts"], "names": [], "mappings": ";;AAgCA,wCAGC;AAUD,oBAqBC;AAhED,uDAAmD;AAInD,4DAGsC;AACtC,uFAAgF;AAiBhF,IAAI,YAAY,GAAkB,KAAK,CAAA;AAKvC,SAAgB,cAAc,CAAC,OAAmB;IAChD,IAAI,CAAC,OAAO;QAAE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;IACjE,YAAY,GAAG,OAAO,CAAA;AACxB,CAAC;AAUM,KAAK,UAAU,IAAI,CAA2B,EACnD,IAAI,EACJ,UAAU,EACV,EAAE,GAAG,QAAQ,GACM;IACnB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,qBAAS,CAAC,IAAI,CACvC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EACb,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EACnB,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,CAC7B,CAAA;IACD,MAAM,SAAS,GAAG;QAChB,CAAC,EAAE,IAAA,sBAAW,EAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC/B,CAAC,EAAE,IAAA,sBAAW,EAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC/B,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;QACvB,OAAO,EAAE,QAAQ;KAClB,CAAA;IACD,OAAO,CAAC,GAAG,EAAE;QACX,IAAI,EAAE,KAAK,OAAO,IAAI,EAAE,KAAK,KAAK;YAChC,OAAO,IAAA,0CAAkB,EAAC,EAAE,GAAG,SAAS,EAAE,EAAE,EAAE,CAAC,CAAA;QACjD,OAAO,SAAS,CAAA;IAClB,CAAC,CAAC,EAAwB,CAAA;AAC5B,CAAC"}