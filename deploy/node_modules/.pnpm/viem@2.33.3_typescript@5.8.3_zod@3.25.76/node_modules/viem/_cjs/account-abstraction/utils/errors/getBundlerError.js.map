{"version": 3, "file": "getBundlerError.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/utils/errors/getBundlerError.ts"], "names": [], "mappings": ";;AAoIA,0CAiLC;AAnTD,wDAyEgC;AAGhC,MAAM,aAAa,GAAG;IACpB,mCAAsB;IACtB,+BAAkB;IAClB,wCAA2B;IAC3B,oCAAuB;IACvB,sCAAyB;IACzB,sCAAyB;IACzB,gDAAmC;IACnC,6CAAgC;IAChC,mDAAsC;IACtC,kDAAqC;IACrC,+CAAkC;CACnC,CAAA;AA0CD,SAAgB,eAAe,CAC7B,GAAc,EACd,IAA+B;IAE/B,MAAM,OAAO,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE,CAAA;IAEjD,IAAI,oCAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC/C,OAAO,IAAI,oCAAuB,CAAC;YACjC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,2CAA8B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACtD,OAAO,IAAI,2CAA8B,CAAC;YACxC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,mCAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC9C,OAAO,IAAI,mCAAsB,CAAC;YAChC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,mCAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC9C,OAAO,IAAI,mCAAsB,CAAC;YAChC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,gCAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC3C,OAAO,IAAI,gCAAmB,CAAC;YAC7B,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAQ,CAAA;IACX,IAAI,0CAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACrD,OAAO,IAAI,0CAA6B,CAAC;YACvC,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAQ,CAAA;IACX,IAAI,0CAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACrD,OAAO,IAAI,0CAA6B,CAAC;YACvC,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAQ,CAAA;IACX,IAAI,qCAAwB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAChD,OAAO,IAAI,qCAAwB,CAAC;YAClC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,kCAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC7C,OAAO,IAAI,kCAAqB,CAAC;YAC/B,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,qCAAwB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAChD,OAAO,IAAI,qCAAwB,CAAC;YAClC,KAAK,EAAE,GAAG;YACV,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAQ,CAAA;IACX,IAAI,mCAAsB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC9C,OAAO,IAAI,mCAAsB,CAAC;YAChC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,oCAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC/C,OAAO,IAAI,oCAAuB,CAAC;YACjC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,yCAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACpD,OAAO,IAAI,yCAA4B,CAAC;YACtC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,wCAA2B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACnD,OAAO,IAAI,wCAA2B,CAAC;YACrC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,2CAA8B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACtD,OAAO,IAAI,2CAA8B,CAAC;YACxC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,sCAAyB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACjD,OAAO,IAAI,sCAAyB,CAAC;YACnC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,iDAAoC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC5D,OAAO,IAAI,iDAAoC,CAAC;YAC9C,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,8CAAiC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACzD,OAAO,IAAI,8CAAiC,CAAC;YAC3C,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,0CAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACrD,OAAO,IAAI,0CAA6B,CAAC;YACvC,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAQ,CAAA;IACX,IAAI,sCAAyB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACjD,OAAO,IAAI,sCAAyB,CAAC;YACnC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,+CAAkC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC1D,OAAO,IAAI,+CAAkC,CAAC;YAC5C,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,iDAAoC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAC5D,OAAO,IAAI,iDAAoC,CAAC;YAC9C,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,wCAA2B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACnD,OAAO,IAAI,wCAA2B,CAAC;YACrC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,8CAAiC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACzD,OAAO,IAAI,8CAAiC,CAAC;YAC3C,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IACX,IAAI,4CAA+B,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QACvD,OAAO,IAAI,4CAA+B,CAAC;YACzC,KAAK,EAAE,GAAG;SACX,CAAQ,CAAA;IAEX,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAC3B,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAM,CAAsB,CAAC,IAAI,CAAC,CAChC,CAAA;IAE5C,IAAI,KAAK,EAAE,CAAC;QACV,IAAI,KAAK,CAAC,IAAI,KAAK,mCAAsB,CAAC,IAAI;YAC5C,OAAO,IAAI,mCAAsB,CAAC;gBAChC,KAAK,EAAE,GAAG;gBACV,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,+BAAkB,CAAC,IAAI;YACxC,OAAO,IAAI,+BAAkB,CAAC;gBAC5B,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,wCAA2B,CAAC,IAAI;YACjD,OAAO,IAAI,wCAA2B,CAAC;gBACrC,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,oCAAuB,CAAC,IAAI;YAC7C,OAAO,IAAI,oCAAuB,CAAC;gBACjC,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,sCAAyB,CAAC,IAAI;YAC/C,OAAO,IAAI,sCAAyB,CAAC;gBACnC,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,sCAAyB,CAAC,IAAI;YAC/C,OAAO,IAAI,sCAAyB,CAAC;gBACnC,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,gDAAmC,CAAC,IAAI;YACzD,OAAO,IAAI,gDAAmC,CAAC;gBAC7C,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,6CAAgC,CAAC,IAAI;YACtD,OAAO,IAAI,6CAAgC,CAAC;gBAC1C,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,mDAAsC,CAAC,IAAI;YAC5D,OAAO,IAAI,mDAAsC,CAAC;gBAChD,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,kDAAqC,CAAC,IAAI;YAC3D,OAAO,IAAI,kDAAqC,CAAC;gBAC/C,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;QACX,IAAI,KAAK,CAAC,IAAI,KAAK,+CAAkC,CAAC,IAAI;YACxD,OAAO,IAAI,+CAAkC,CAAC;gBAC5C,KAAK,EAAE,GAAG;aACX,CAAQ,CAAA;IACb,CAAC;IAED,OAAO,IAAI,gCAAmB,CAAC;QAC7B,KAAK,EAAE,GAAG;KACX,CAAQ,CAAA;AACX,CAAC"}