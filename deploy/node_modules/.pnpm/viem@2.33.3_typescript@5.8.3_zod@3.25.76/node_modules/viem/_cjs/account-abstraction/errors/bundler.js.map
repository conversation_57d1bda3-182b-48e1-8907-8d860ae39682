{"version": 3, "file": "bundler.js", "sourceRoot": "", "sources": ["../../../account-abstraction/errors/bundler.ts"], "names": [], "mappings": ";;;AACA,kDAAgD;AAMhD,MAAa,uBAAwB,SAAQ,mBAAS;IAEpD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,gCAAgC,EAAE;YACtC,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,kGAAkG;gBAClG,8CAA8C;aAC/C;YACD,IAAI,EAAE,yBAAyB;SAChC,CAAC,CAAA;IACJ,CAAC;;AAhBH,0DAiBC;AAhBQ;;;;WAAU,MAAM;GAAA;AAsBzB,MAAa,sBAAuB,SAAQ,mBAAS;IAMnD,YAAY,EACV,KAAK,EACL,IAAI,EACJ,OAAO,MAKL,EAAE;QACJ,MAAM,MAAM,GAAG,OAAO;YACpB,EAAE,OAAO,CAAC,sBAAsB,EAAE,EAAE,CAAC;YACrC,EAAE,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAA;QACrC,KAAK,CACH,sBACE,MAAM,CAAC,CAAC,CAAC,gBAAgB,MAAM,EAAE,CAAC,CAAC,CAAC,uBACtC,GAAG,EACH;YACE,KAAK;YACL,IAAI,EAAE,wBAAwB;SAC/B,CACF,CAAA;QAtBH;;;;;WAAuC;QAwBrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;;AA7BH,wDA8BC;AA7BQ;;;;WAAO,CAAC,KAAK;EAAT,CAAS;AACb;;;;WAAU,oBAAoB;EAAvB,CAAuB;AAkCvC,MAAa,8BAA+B,SAAQ,mBAAS;IAE3D,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,sCAAsC,EAAE;YAC5C,KAAK;YACL,IAAI,EAAE,gCAAgC;SACvC,CAAC,CAAA;IACJ,CAAC;;AAXH,wEAYC;AAXQ;;;;WAAU,MAAM;GAAA;AAgBzB,MAAa,sBAAuB,SAAQ,mBAAS;IAEnD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,uBAAuB,EAAE;YAC7B,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,mDAAmD;aACpD,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,wBAAwB;SAC/B,CAAC,CAAA;IACJ,CAAC;;AAfH,wDAgBC;AAfQ;;;;WAAU,MAAM;GAAA;AAoBzB,MAAa,sBAAuB,SAAQ,mBAAS;IAEnD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CACH,8EAA8E,EAC9E;YACE,KAAK;YACL,IAAI,EAAE,wBAAwB;SAC/B,CACF,CAAA;IACH,CAAC;;AAdH,wDAeC;AAdQ;;;;WAAU,MAAM;GAAA;AAmBzB,MAAa,mBAAoB,SAAQ,mBAAS;IAEhD,YAAY,EACV,KAAK,EACL,OAAO,EACP,WAAW,EACX,QAAQ,GAMT;QACC,KAAK,CAAC,kDAAkD,EAAE;YACxD,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,wEAAwE;gBACxE,wFAAwF;gBACxF,+DAA+D;gBAC/D,OAAO,IAAI,YAAY,OAAO,EAAE;gBAChC,WAAW,IAAI,gBAAgB,WAAW,EAAE;gBAC5C,QAAQ,IAAI,aAAa,QAAQ,EAAE;aACpC,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,qBAAqB;SAC5B,CAAC,CAAA;IACJ,CAAC;;AA1BH,kDA2BC;AA1BQ;;;;WAAU,MAAM;GAAA;AAgCzB,MAAa,6BAA8B,SAAQ,mBAAS;IAE1D,YAAY,EACV,KAAK,EACL,OAAO,EACP,WAAW,EACX,QAAQ,GAMT;QACC,KAAK,CACH,wEAAwE,EACxE;YACE,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,gEAAgE;gBAChE,8DAA8D;gBAC9D,OAAO,IAAI,YAAY,OAAO,EAAE;gBAChC,WAAW,IAAI,gBAAgB,WAAW,EAAE;gBAC5C,QAAQ,IAAI,aAAa,QAAQ,EAAE;aACpC,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,+BAA+B;SACtC,CACF,CAAA;IACH,CAAC;;AA5BH,sEA6BC;AA5BQ;;;;WAAU,MAAM;GAAA;AAkCzB,MAAa,6BAA8B,SAAQ,mBAAS;IAE1D,YAAY,EACV,KAAK,EACL,OAAO,EACP,WAAW,EACX,QAAQ,EACR,MAAM,GAOP;QACC,KAAK,CACH,kFAAkF,EAClF;YACE,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,gFAAgF;gBAChF,OAAO,IAAI,YAAY,OAAO,EAAE;gBAChC,WAAW,IAAI,gBAAgB,WAAW,EAAE;gBAC5C,QAAQ,IAAI,aAAa,QAAQ,EAAE;gBACnC,MAAM,IAAI,WAAW,MAAM,EAAE;aAC9B,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,+BAA+B;SACtC,CACF,CAAA;IACH,CAAC;;AA9BH,sEA+BC;AA9BQ;;;;WAAU,MAAM;GAAA;AAmCzB,MAAa,wBAAyB,SAAQ,mBAAS;IAErD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CACH,6EAA6E,EAC7E;YACE,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,sFAAsF;gBACtF,gCAAgC;aACjC,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,0BAA0B;SACjC,CACF,CAAA;IACH,CAAC;;AAnBH,4DAoBC;AAnBQ;;;;WAAU,MAAM;GAAA;AAwBzB,MAAa,qBAAsB,SAAQ,mBAAS;IAElD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,kEAAkE,EAAE;YACxE,KAAK;YACL,IAAI,EAAE,uBAAuB;SAC9B,CAAC,CAAA;IACJ,CAAC;;AAXH,sDAYC;AAXQ;;;;WAAU,MAAM;GAAA;AAgBzB,MAAa,sBAAuB,SAAQ,mBAAS;IAEnD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CACH,6EAA6E,EAC7E;YACE,KAAK;YACL,IAAI,EAAE,wBAAwB;SAC/B,CACF,CAAA;IACH,CAAC;;AAdH,wDAeC;AAdQ;;;;WAAU,MAAM;GAAA;AAmBzB,MAAa,wBAAyB,SAAQ,mBAAS;IAErD,YAAY,EACV,KAAK,EACL,KAAK,GAIN;QACC,KAAK,CAAC,sDAAsD,EAAE;YAC5D,KAAK;YACL,YAAY,EAAE,CAAC,KAAK,IAAI,UAAU,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAa;YACtE,IAAI,EAAE,0BAA0B;SACjC,CAAC,CAAA;IACJ,CAAC;;AAdH,4DAeC;AAdQ;;;;WAAU,MAAM;GAAA;AAmBzB,MAAa,uBAAwB,SAAQ,mBAAS;IAEpD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,4CAA4C,EAAE;YAClD,KAAK;YACL,IAAI,EAAE,yBAAyB;SAChC,CAAC,CAAA;IACJ,CAAC;;AAXH,0DAYC;AAXQ;;;;WAAU,MAAM;GAAA;AAgBzB,MAAa,kBAAmB,SAAQ,mBAAS;IAG/C,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,uCAAuC,EAAE;YAC7C,KAAK;YACL,IAAI,EAAE,oBAAoB;SAC3B,CAAC,CAAA;IACJ,CAAC;;AAZH,gDAaC;AAZQ;;;;WAAO,CAAC,KAAK;GAAA;AAiBtB,MAAa,4BAA6B,SAAQ,mBAAS;IAEzD,YAAY,EACV,KAAK,EACL,gBAAgB,GAIjB;QACC,KAAK,CAAC,4CAA4C,EAAE;YAClD,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,+DAA+D;gBAC/D,gBAAgB,IAAI,qBAAqB,gBAAgB,EAAE;aAC5D,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,8BAA8B;SACrC,CAAC,CAAA;IACJ,CAAC;;AAlBH,oEAmBC;AAlBQ;;;;WAAU,MAAM;GAAA;AAwBzB,MAAa,2BAA4B,SAAQ,mBAAS;IAIxD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,sDAAsD,EAAE;YAC5D,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,wFAAwF;aACzF,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,6BAA6B;SACpC,CAAC,CAAA;IACJ,CAAC;;AAjBH,kEAkBC;AAjBQ;;;;WAAO,CAAC,KAAK;GAAA;AACb;;;;WAAU,MAAM;GAAA;AAsBzB,MAAa,8BAA+B,SAAQ,mBAAS;IAE3D,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,mEAAmE,EAAE;YACzE,KAAK;YACL,IAAI,EAAE,gCAAgC;SACvC,CAAC,CAAA;IACJ,CAAC;;AAXH,wEAYC;AAXQ;;;;WAAU,MAAM;GAAA;AAgBzB,MAAa,yBAA0B,SAAQ,mBAAS;IAEtD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,+CAA+C,EAAE;YACrD,KAAK;YACL,IAAI,EAAE,2BAA2B;SAClC,CAAC,CAAA;IACJ,CAAC;;AAXH,8DAYC;AAXQ;;;;WAAU,MAAM;GAAA;AAiBzB,MAAa,uBAAwB,SAAQ,mBAAS;IAGpD,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CACH,yFAAyF,EACzF;YACE,KAAK;YACL,IAAI,EAAE,yBAAyB;SAChC,CACF,CAAA;IACH,CAAC;;AAXH,0DAYC;AAXQ;;;;WAAO,CAAC,KAAK;GAAA;AAiBtB,MAAa,yBAA0B,SAAQ,mBAAS;IAGtD,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CACH,yFAAyF,EACzF;YACE,KAAK;YACL,IAAI,EAAE,2BAA2B;SAClC,CACF,CAAA;IACH,CAAC;;AAXH,8DAYC;AAXQ;;;;WAAO,CAAC,KAAK;GAAA;AAiBtB,MAAa,oCAAqC,SAAQ,mBAAS;IAEjE,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,uCAAuC,EAAE;YAC7C,KAAK;YACL,IAAI,EAAE,sCAAsC;SAC7C,CAAC,CAAA;IACJ,CAAC;;AAXH,oFAYC;AAXQ;;;;WAAU,MAAM;GAAA;AAiBzB,MAAa,6BAA8B,SAAQ,mBAAS;IAE1D,YAAY,EACV,KAAK,EACL,OAAO,EACP,WAAW,EACX,QAAQ,GAMT;QACC,KAAK,CAAC,0CAA0C,EAAE;YAChD,KAAK;YACL,YAAY,EAAE;gBACZ,gDAAgD;gBAChD,OAAO,IAAI,WAAW;gBACtB,WAAW,IAAI,eAAe;gBAC9B,QAAQ,IAAI,YAAY;aACzB,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,+BAA+B;SACtC,CAAC,CAAA;IACJ,CAAC;;AAvBH,sEAwBC;AAvBQ;;;;WAAU,MAAM;GAAA;AA6BzB,MAAa,yBAA0B,SAAQ,mBAAS;IAGtD,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CACH,sIAAsI,EACtI;YACE,KAAK;YACL,IAAI,EAAE,2BAA2B;SAClC,CACF,CAAA;IACH,CAAC;;AAXH,8DAYC;AAXQ;;;;WAAO,CAAC,KAAK;GAAA;AAiBtB,MAAa,iCAAkC,SAAQ,mBAAS;IAE9D,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,8DAA8D,EAAE;YACpE,KAAK;YACL,IAAI,EAAE,mCAAmC;SAC1C,CAAC,CAAA;IACJ,CAAC;;AAXH,8EAYC;AAXQ;;;;WAAU,MAAM;GAAA;AAkBzB,MAAa,mCAAoC,SAAQ,mBAAS;IAGhE,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CACH,oFAAoF,EACpF;YACE,KAAK;YACL,IAAI,EAAE,qCAAqC;SAC5C,CACF,CAAA;IACH,CAAC;;AAXH,kFAYC;AAXQ;;;;WAAO,CAAC,KAAK;GAAA;AAgBtB,MAAa,yBAA0B,SAAQ,mBAAS;IAEtD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,yBAAyB,EAAE;YAC/B,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,iHAAiH;aAClH,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,2BAA2B;SAClC,CAAC,CAAA;IACJ,CAAC;;AAfH,8DAgBC;AAfQ;;;;WAAU,MAAM;GAAA;AAqBzB,MAAa,kCAAmC,SAAQ,mBAAS;IAE/D,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,uCAAuC,EAAE;YAC7C,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,sHAAsH;aACvH,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,oCAAoC;SAC3C,CAAC,CAAA;IACJ,CAAC;;AAfH,gFAgBC;AAfQ;;;;WAAU,MAAM;GAAA;AAoBzB,MAAa,2BAA4B,SAAQ,mBAAS;IAExD,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,uDAAuD,EAAE;YAC7D,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,kHAAkH;aACnH,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,6BAA6B;SACpC,CAAC,CAAA;IACJ,CAAC;;AAfH,kEAgBC;AAfQ;;;;WAAU,MAAM;GAAA;AAqBzB,MAAa,oCAAqC,SAAQ,mBAAS;IAEjE,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,uDAAuD,EAAE;YAC7D,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,8GAA8G;aAC/G,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,sCAAsC;SAC7C,CAAC,CAAA;IACJ,CAAC;;AAfH,oFAgBC;AAfQ;;;;WAAU,MAAM;GAAA;AAsBzB,MAAa,sCAAuC,SAAQ,mBAAS;IAGnE,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CACH,qGAAqG,EACrG;YACE,KAAK;YACL,IAAI,EAAE,wCAAwC;SAC/C,CACF,CAAA;IACH,CAAC;;AAXH,wFAYC;AAXQ;;;;WAAO,CAAC,KAAK;GAAA;AAkBtB,MAAa,qCAAsC,SAAQ,mBAAS;IAGlE,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CAAC,mEAAmE,EAAE;YACzE,KAAK;YACL,IAAI,EAAE,uCAAuC;SAC9C,CAAC,CAAA;IACJ,CAAC;;AARH,sFASC;AARQ;;;;WAAO,CAAC,KAAK;GAAA;AAetB,MAAa,kCAAmC,SAAQ,mBAAS;IAG/D,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CAAC,wDAAwD,EAAE;YAC9D,KAAK;YACL,IAAI,EAAE,oCAAoC;SAC3C,CAAC,CAAA;IACJ,CAAC;;AARH,gFASC;AARQ;;;;WAAO,CAAC,KAAK;GAAA;AAetB,MAAa,gCAAiC,SAAQ,mBAAS;IAG7D,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CACH,qIAAqI,EACrI;YACE,KAAK;YACL,IAAI,EAAE,kCAAkC;SACzC,CACF,CAAA;IACH,CAAC;;AAXH,4EAYC;AAXQ;;;;WAAO,CAAC,KAAK;GAAA;AAgBtB,MAAa,mBAAoB,SAAQ,mBAAS;IAChD,YAAY,EAAE,KAAK,EAAqC;QACtD,KAAK,CACH,qDAAqD,KAAK,EAAE,YAAY,EAAE,EAC1E;YACE,KAAK;YACL,IAAI,EAAE,qBAAqB;SAC5B,CACF,CAAA;IACH,CAAC;CACF;AAVD,kDAUC;AAMD,MAAa,iCAAkC,SAAQ,mBAAS;IAE9D,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,iDAAiD,EAAE;YACvD,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,qEAAqE;aACtE,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,mCAAmC;SAC1C,CAAC,CAAA;IACJ,CAAC;;AAfH,8EAgBC;AAfQ;;;;WAAU,MAAM;GAAA;AAqBzB,MAAa,+BAAgC,SAAQ,mBAAS;IAE5D,YAAY,EACV,KAAK,GAGN;QACC,KAAK,CAAC,mDAAmD,EAAE;YACzD,KAAK;YACL,YAAY,EAAE;gBACZ,wBAAwB;gBACxB,sEAAsE;aACvE,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,iCAAiC;SACxC,CAAC,CAAA;IACJ,CAAC;;AAfH,0EAgBC;AAfQ;;;;WAAU,MAAM;GAAA"}