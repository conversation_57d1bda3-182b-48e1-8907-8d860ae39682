"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toSoladySmartAccount = toSoladySmartAccount;
const parseAccount_js_1 = require("../../../accounts/utils/parseAccount.js");
const readContract_js_1 = require("../../../actions/public/readContract.js");
const signMessage_js_1 = require("../../../actions/wallet/signMessage.js");
const address_js_1 = require("../../../constants/address.js");
const base_js_1 = require("../../../errors/base.js");
const signMessage_js_2 = require("../../../experimental/erc7739/actions/signMessage.js");
const signTypedData_js_1 = require("../../../experimental/erc7739/actions/signTypedData.js");
const decodeFunctionData_js_1 = require("../../../utils/abi/decodeFunctionData.js");
const encodeFunctionData_js_1 = require("../../../utils/abi/encodeFunctionData.js");
const pad_js_1 = require("../../../utils/data/pad.js");
const getAction_js_1 = require("../../../utils/getAction.js");
const abis_js_1 = require("../../constants/abis.js");
const getUserOperationHash_js_1 = require("../../utils/userOperation/getUserOperationHash.js");
const toSmartAccount_js_1 = require("../toSmartAccount.js");
async function toSoladySmartAccount(parameters) {
    const { address, client, entryPoint: entryPoint_ = {
        abi: abis_js_1.entryPoint07Abi,
        address: address_js_1.entryPoint07Address,
        version: '0.7',
    }, factoryAddress = '0x5d82735936c6Cd5DE57cC3c1A799f6B2E6F933Df', getNonce, salt = '0x0', } = parameters;
    const entryPoint = {
        abi: entryPoint_.abi,
        address: entryPoint_.address,
        version: entryPoint_.version,
    };
    const factory = {
        abi: factoryAbi,
        address: factoryAddress,
    };
    const owner = (0, parseAccount_js_1.parseAccount)(parameters.owner);
    return (0, toSmartAccount_js_1.toSmartAccount)({
        client,
        entryPoint,
        getNonce,
        extend: { abi, factory },
        async decodeCalls(data) {
            const result = (0, decodeFunctionData_js_1.decodeFunctionData)({
                abi,
                data,
            });
            if (result.functionName === 'execute')
                return [
                    { to: result.args[0], value: result.args[1], data: result.args[2] },
                ];
            if (result.functionName === 'executeBatch')
                return result.args[0].map((arg) => ({
                    to: arg.target,
                    value: arg.value,
                    data: arg.data,
                }));
            throw new base_js_1.BaseError(`unable to decode calls for "${result.functionName}"`);
        },
        async encodeCalls(calls) {
            if (calls.length === 1)
                return (0, encodeFunctionData_js_1.encodeFunctionData)({
                    abi,
                    functionName: 'execute',
                    args: [calls[0].to, calls[0].value ?? 0n, calls[0].data ?? '0x'],
                });
            return (0, encodeFunctionData_js_1.encodeFunctionData)({
                abi,
                functionName: 'executeBatch',
                args: [
                    calls.map((call) => ({
                        data: call.data ?? '0x',
                        target: call.to,
                        value: call.value ?? 0n,
                    })),
                ],
            });
        },
        async getAddress() {
            if (address)
                return address;
            return await (0, readContract_js_1.readContract)(client, {
                ...factory,
                functionName: 'getAddress',
                args: [(0, pad_js_1.pad)(salt)],
            });
        },
        async getFactoryArgs() {
            const factoryData = (0, encodeFunctionData_js_1.encodeFunctionData)({
                abi: factory.abi,
                functionName: 'createAccount',
                args: [owner.address, (0, pad_js_1.pad)(salt)],
            });
            return { factory: factory.address, factoryData };
        },
        async getStubSignature() {
            return '0xfffffffffffffffffffffffffffffff0000000000000000000000000000000007aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1c';
        },
        async signMessage(parameters) {
            const { message } = parameters;
            const [address, { factory, factoryData }] = await Promise.all([
                this.getAddress(),
                this.getFactoryArgs(),
            ]);
            return await (0, signMessage_js_2.signMessage)(client, {
                account: owner,
                factory,
                factoryData,
                message,
                verifier: address,
            });
        },
        async signTypedData(parameters) {
            const { domain, types, primaryType, message } = parameters;
            const [address, { factory, factoryData }] = await Promise.all([
                this.getAddress(),
                this.getFactoryArgs(),
            ]);
            return await (0, signTypedData_js_1.signTypedData)(client, {
                account: owner,
                domain,
                message,
                factory,
                factoryData,
                primaryType,
                types,
                verifier: address,
            });
        },
        async signUserOperation(parameters) {
            const { chainId = client.chain.id, ...userOperation } = parameters;
            const address = await this.getAddress();
            const userOpHash = (0, getUserOperationHash_js_1.getUserOperationHash)({
                chainId,
                entryPointAddress: entryPoint.address,
                entryPointVersion: entryPoint.version,
                userOperation: {
                    ...userOperation,
                    sender: address,
                },
            });
            const signature = await (0, getAction_js_1.getAction)(client, signMessage_js_1.signMessage, 'signMessage')({
                account: owner,
                message: {
                    raw: userOpHash,
                },
            });
            return signature;
        },
    });
}
const abi = [
    {
        type: 'fallback',
        stateMutability: 'payable',
    },
    {
        type: 'receive',
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'addDeposit',
        inputs: [],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'cancelOwnershipHandover',
        inputs: [],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'completeOwnershipHandover',
        inputs: [
            {
                name: 'pendingOwner',
                type: 'address',
            },
        ],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'delegateExecute',
        inputs: [
            {
                name: 'delegate',
                type: 'address',
            },
            {
                name: 'data',
                type: 'bytes',
            },
        ],
        outputs: [
            {
                name: 'result',
                type: 'bytes',
            },
        ],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'eip712Domain',
        inputs: [],
        outputs: [
            {
                name: 'name',
                type: 'string',
            },
            {
                name: 'version',
                type: 'string',
            },
            {
                name: 'chainId',
                type: 'uint256',
            },
            {
                name: 'verifyingContract',
                type: 'address',
            },
            {
                name: 'salt',
                type: 'bytes32',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'entryPoint',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'execute',
        inputs: [
            {
                name: 'target',
                type: 'address',
            },
            {
                name: 'value',
                type: 'uint256',
            },
            {
                name: 'data',
                type: 'bytes',
            },
        ],
        outputs: [
            {
                name: 'result',
                type: 'bytes',
            },
        ],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'executeBatch',
        inputs: [
            {
                name: 'calls',
                type: 'tuple[]',
                components: [
                    {
                        name: 'target',
                        type: 'address',
                    },
                    {
                        name: 'value',
                        type: 'uint256',
                    },
                    {
                        name: 'data',
                        type: 'bytes',
                    },
                ],
            },
        ],
        outputs: [
            {
                name: 'results',
                type: 'bytes[]',
            },
        ],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'getDeposit',
        inputs: [],
        outputs: [
            {
                name: 'result',
                type: 'uint256',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'initialize',
        inputs: [
            {
                name: 'newOwner',
                type: 'address',
            },
        ],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'isValidSignature',
        inputs: [
            {
                name: 'hash',
                type: 'bytes32',
            },
            {
                name: 'signature',
                type: 'bytes',
            },
        ],
        outputs: [
            {
                name: 'result',
                type: 'bytes4',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'owner',
        inputs: [],
        outputs: [
            {
                name: 'result',
                type: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'ownershipHandoverExpiresAt',
        inputs: [
            {
                name: 'pendingOwner',
                type: 'address',
            },
        ],
        outputs: [
            {
                name: 'result',
                type: 'uint256',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'proxiableUUID',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'bytes32',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'renounceOwnership',
        inputs: [],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'requestOwnershipHandover',
        inputs: [],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'storageLoad',
        inputs: [
            {
                name: 'storageSlot',
                type: 'bytes32',
            },
        ],
        outputs: [
            {
                name: 'result',
                type: 'bytes32',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'storageStore',
        inputs: [
            {
                name: 'storageSlot',
                type: 'bytes32',
            },
            {
                name: 'storageValue',
                type: 'bytes32',
            },
        ],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'transferOwnership',
        inputs: [
            {
                name: 'newOwner',
                type: 'address',
            },
        ],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'upgradeToAndCall',
        inputs: [
            {
                name: 'newImplementation',
                type: 'address',
            },
            {
                name: 'data',
                type: 'bytes',
            },
        ],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'validateUserOp',
        inputs: [
            {
                name: 'userOp',
                type: 'tuple',
                components: [
                    {
                        name: 'sender',
                        type: 'address',
                    },
                    {
                        name: 'nonce',
                        type: 'uint256',
                    },
                    {
                        name: 'initCode',
                        type: 'bytes',
                    },
                    {
                        name: 'callData',
                        type: 'bytes',
                    },
                    {
                        name: 'accountGasLimits',
                        type: 'bytes32',
                    },
                    {
                        name: 'preVerificationGas',
                        type: 'uint256',
                    },
                    {
                        name: 'gasFees',
                        type: 'bytes32',
                    },
                    {
                        name: 'paymasterAndData',
                        type: 'bytes',
                    },
                    {
                        name: 'signature',
                        type: 'bytes',
                    },
                ],
            },
            {
                name: 'userOpHash',
                type: 'bytes32',
            },
            {
                name: 'missingAccountFunds',
                type: 'uint256',
            },
        ],
        outputs: [
            {
                name: 'validationData',
                type: 'uint256',
            },
        ],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'withdrawDepositTo',
        inputs: [
            {
                name: 'to',
                type: 'address',
            },
            {
                name: 'amount',
                type: 'uint256',
            },
        ],
        outputs: [],
        stateMutability: 'payable',
    },
    {
        type: 'event',
        name: 'OwnershipHandoverCanceled',
        inputs: [
            {
                name: 'pendingOwner',
                type: 'address',
                indexed: true,
            },
        ],
        anonymous: false,
    },
    {
        type: 'event',
        name: 'OwnershipHandoverRequested',
        inputs: [
            {
                name: 'pendingOwner',
                type: 'address',
                indexed: true,
            },
        ],
        anonymous: false,
    },
    {
        type: 'event',
        name: 'OwnershipTransferred',
        inputs: [
            {
                name: 'oldOwner',
                type: 'address',
                indexed: true,
            },
            {
                name: 'newOwner',
                type: 'address',
                indexed: true,
            },
        ],
        anonymous: false,
    },
    {
        type: 'event',
        name: 'Upgraded',
        inputs: [
            {
                name: 'implementation',
                type: 'address',
                indexed: true,
            },
        ],
        anonymous: false,
    },
    {
        type: 'error',
        name: 'AlreadyInitialized',
        inputs: [],
    },
    {
        type: 'error',
        name: 'FnSelectorNotRecognized',
        inputs: [],
    },
    {
        type: 'error',
        name: 'NewOwnerIsZeroAddress',
        inputs: [],
    },
    {
        type: 'error',
        name: 'NoHandoverRequest',
        inputs: [],
    },
    {
        type: 'error',
        name: 'Unauthorized',
        inputs: [],
    },
    {
        type: 'error',
        name: 'UnauthorizedCallContext',
        inputs: [],
    },
    {
        type: 'error',
        name: 'UpgradeFailed',
        inputs: [],
    },
];
const factoryAbi = [
    {
        type: 'constructor',
        inputs: [
            {
                name: 'erc4337',
                type: 'address',
            },
        ],
        stateMutability: 'nonpayable',
    },
    {
        type: 'function',
        name: 'createAccount',
        inputs: [
            {
                name: 'owner',
                type: 'address',
            },
            {
                name: 'salt',
                type: 'bytes32',
            },
        ],
        outputs: [
            {
                name: '',
                type: 'address',
            },
        ],
        stateMutability: 'payable',
    },
    {
        type: 'function',
        name: 'getAddress',
        inputs: [
            {
                name: 'salt',
                type: 'bytes32',
            },
        ],
        outputs: [
            {
                name: '',
                type: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'implementation',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'address',
            },
        ],
        stateMutability: 'view',
    },
    {
        type: 'function',
        name: 'initCodeHash',
        inputs: [],
        outputs: [
            {
                name: '',
                type: 'bytes32',
            },
        ],
        stateMutability: 'view',
    },
];
//# sourceMappingURL=toSoladySmartAccount.js.map