{"version": 3, "file": "userOperation.js", "sourceRoot": "", "sources": ["../../../account-abstraction/errors/userOperation.ts"], "names": [], "mappings": ";;;AAAA,kDAAgD;AAChD,gEAAyD;AAEzD,mDAAiD;AAMjD,MAAa,2BAA4B,SAAQ,mBAAS;IAGxD,YACE,KAAgB,EAChB,EACE,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,WAAW,EACX,QAAQ,EACR,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,SAAS,EACT,gBAAgB,EAChB,aAAa,EACb,uBAAuB,EACvB,6BAA6B,EAC7B,kBAAkB,EAClB,MAAM,EACN,SAAS,EACT,oBAAoB,GAGrB;QAED,MAAM,UAAU,GAAG,IAAA,4BAAW,EAAC;YAC7B,QAAQ;YACR,YAAY;YACZ,OAAO;YACP,WAAW;YACX,QAAQ;YACR,YAAY,EACV,OAAO,YAAY,KAAK,WAAW;gBACnC,GAAG,IAAA,qBAAU,EAAC,YAAY,CAAC,OAAO;YACpC,oBAAoB,EAClB,OAAO,oBAAoB,KAAK,WAAW;gBAC3C,GAAG,IAAA,qBAAU,EAAC,oBAAoB,CAAC,OAAO;YAC5C,KAAK;YACL,SAAS;YACT,gBAAgB;YAChB,aAAa;YACb,uBAAuB;YACvB,6BAA6B;YAC7B,kBAAkB;YAClB,MAAM;YACN,SAAS;YACT,oBAAoB;SACrB,CAAC,CAAA;QAEF,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE;YACxB,KAAK;YACL,QAAQ;YACR,YAAY,EAAE;gBACZ,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3D,oBAAoB;gBACpB,UAAU;aACX,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,6BAA6B;SACpC,CAAC,CAAA;QA5DK;;;;;WAAgB;QA6DvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;IACpB,CAAC;CACF;AAhED,kEAgEC;AAMD,MAAa,iCAAkC,SAAQ,mBAAS;IAC9D,YAAY,EAAE,IAAI,EAAkB;QAClC,KAAK,CACH,qCAAqC,IAAI,2EAA2E,EACpH,EAAE,IAAI,EAAE,mCAAmC,EAAE,CAC9C,CAAA;IACH,CAAC;CACF;AAPD,8EAOC;AAKD,MAAa,0BAA2B,SAAQ,mBAAS;IACvD,YAAY,EAAE,IAAI,EAAkB;QAClC,KAAK,CAAC,6BAA6B,IAAI,uBAAuB,EAAE;YAC9D,IAAI,EAAE,4BAA4B;SACnC,CAAC,CAAA;IACJ,CAAC;CACF;AAND,gEAMC;AAMD,MAAa,uCAAwC,SAAQ,mBAAS;IACpE,YAAY,EAAE,IAAI,EAAkB;QAClC,KAAK,CACH,yDAAyD,IAAI,oBAAoB,EACjF,EAAE,IAAI,EAAE,yCAAyC,EAAE,CACpD,CAAA;IACH,CAAC;CACF;AAPD,0FAOC"}