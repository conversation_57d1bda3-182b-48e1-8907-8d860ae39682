{"name": "brotli-wasm", "version": "2.0.1", "description": "A reliable compressor and decompressor for Brotli, supporting node & browsers via wasm", "types": "./index.d.ts", "main": "./index.node.js", "browser": "./index.browser.js", "exports": {".": {"types": "./index.d.ts", "import": "./index.web.js", "browser": "./index.browser.js", "require": "./index.node.js", "default": "./index.web.js"}}, "sideEffects": false, "files": ["pkg.node", "pkg.bundler", "pkg.web", "index.node.js", "index.browser.js", "index.web.js", "index.d.ts"], "scripts": {"build": "node ./build.js", "pretest": "npm run build", "test": "npm run test:node && npm run test:esm && npm run test:webpack", "test:node": "ts-mocha -p test/tsconfig.json 'test/**/*.spec.ts'", "test:webpack": "karma start ./karma-webpack.conf.js", "test:esm": "karma start ./karma-esm.conf.js", "test:webpack:debug": "npm run test:webpack -- --single-run=false --browsers Chrome", "test:esm:debug": "npm run test:esm -- --single-run=false --browsers Chrome"}, "repository": {"type": "git", "url": "git+ssh://**************/httptoolkit/brotli-wasm.git"}, "keywords": ["brotli", "wasm", "compression", "decompression"], "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/httptoolkit/brotli-wasm/issues"}, "homepage": "https://github.com/httptoolkit/brotli-wasm#readme", "devDependencies": {"@peculiar/webcrypto": "^1.4.0", "@types/atob": "^2.1.2", "@types/btoa": "^1.2.3", "@types/chai": "^4.2.18", "@types/mocha": "^8.2.2", "@types/node": "^15.6.0", "@types/text-encoding": "0.0.36", "atob": "^2.1.2", "btoa": "^1.2.1", "buffer": "^6.0.3", "chai": "^4.3.4", "karma": "^6.3.2", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-sourcemap-loader": "^0.3.8", "karma-spec-reporter": "0.0.32", "karma-typescript": "^5.5.1", "karma-vite": "^1.0.1", "karma-webpack": "^5.0.0", "mocha": "^8.4.0", "shelljs": "^0.8.4", "text-encoding": "^0.7.0", "ts-loader": "^9.2.1", "ts-mocha": "^10.0.0", "ts-node": "^9.1.1", "typescript": "^4.2.4", "wasm-pack": "^0.10.3", "webpack": "^5.37.1"}}