#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/@ethereumjs+rlp@4.0.1/node_modules/@ethereumjs/rlp/bin/node_modules:/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/@ethereumjs+rlp@4.0.1/node_modules/@ethereumjs/rlp/node_modules:/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/@ethereumjs+rlp@4.0.1/node_modules/@ethereumjs/node_modules:/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/@ethereumjs+rlp@4.0.1/node_modules:/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/@ethereumjs+rlp@4.0.1/node_modules/@ethereumjs/rlp/bin/node_modules:/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/@ethereumjs+rlp@4.0.1/node_modules/@ethereumjs/rlp/node_modules:/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/@ethereumjs+rlp@4.0.1/node_modules/@ethereumjs/node_modules:/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/@ethereumjs+rlp@4.0.1/node_modules:/home/<USER>/Desktop/codes/hackathons/25/avax-mumbai/deploy/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@ethereumjs+rlp@4.0.1/node_modules/@ethereumjs/rlp/bin/rlp" "$@"
else
  exec node  "$basedir/../../../../../../@ethereumjs+rlp@4.0.1/node_modules/@ethereumjs/rlp/bin/rlp" "$@"
fi
