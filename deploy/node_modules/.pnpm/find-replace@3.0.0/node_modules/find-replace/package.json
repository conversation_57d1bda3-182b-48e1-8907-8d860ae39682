{"name": "find-replace", "author": "<PERSON> <<EMAIL>>", "version": "3.0.0", "description": "Find and either replace or remove items in an array", "repository": "https://github.com/75lb/find-replace.git", "license": "MIT", "main": "dist/index.js", "keywords": ["find", "replace", "array", "remove", "splice"], "engines": {"node": ">=4.0.0"}, "scripts": {"test": "test-runner test.js", "docs": "jsdoc2md -c jsdoc.conf -t README.hbs index.mjs > README.md; echo", "dist": "rollup index.mjs -f umd -n findReplace -o dist/index.js && rollup index.mjs -f esm -o dist/index.mjs"}, "devDependencies": {"jsdoc-to-markdown": "^4.0.1", "rollup": "^1.7.0", "test-runner": "^0.5.1"}, "dependencies": {"array-back": "^3.0.1"}, "files": ["index.mjs", "dist"]}