{"name": "@solidity-parser/parser", "version": "0.20.2", "description": "A Solidity parser built from a robust ANTLR 4 grammar", "main": "./dist/index.cjs.js", "browser": "./dist/index.umd.js", "unpkg": "./dist/index.umd.js", "files": ["dist/**/*", "src/**/*"], "types": "dist/src/index.d.ts", "scripts": {"antlr": "antlr4 -Dlanguage=TypeScript -visitor -o src/ antlr/Solidity.g4 && npm run tokens", "tokens": "node ./scripts/tokens-string.js", "build:browser": "node ./scripts/build-browser.js", "build:node": "node ./scripts/build-node.js", "build": "npm run antlr && npm run build:node && npm run build:browser && npm run generate-types", "generate-types": "tsc", "eslint": "eslint 'src/**' 'test/**'", "lint": "npm run eslint && npm run prettier -- --list-different", "lint:fix": "npm run eslint -- --fix && npm run prettier -- --write", "prettier": "prettier './*.{js,json,md}' '{src,test}/**/*.ts'", "prepack": "npm run build", "test": "npm run test:node && npm run test:browser", "test:node": "mocha", "test:browser": "karma start karma.conf.js", "test:integration": "mocha --config ./.mocharc-integration.js test-integration", "test:coverage": "nyc mocha"}, "authors": ["<PERSON>", "<PERSON>", "Gon<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "homepage": "https://github.com/solidity-parser/parser", "repository": {"type": "git", "url": "https://github.com/solidity-parser/parser"}, "license": "MIT", "devDependencies": {"@types/chai": "^4.3.12", "@types/mocha": "^10.0.6", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "antlr4": "^4.13.1-patch-1", "browserslist": "^4.23.0", "chai": "^4.4.1", "esbuild": "^0.20.1", "esbuild-plugin-browserslist": "^0.11.1", "eslint": "^8.57.0", "karma": "^6.4.3", "karma-chrome-launcher": "^3.2.0", "karma-mocha": "^2.0.1", "mocha": "^10.3.0", "nyc": "^15.1.0", "prettier": "^3.2.5", "puppeteer": "^22.4.1", "ts-node": "^10.9.2", "typescript": "^5.4.2"}, "nyc": {"extension": [".ts"]}}